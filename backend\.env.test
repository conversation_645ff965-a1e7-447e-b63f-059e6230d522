# Test environment configuration

# Node environment
NODE_ENV=test

# Server configuration
PORT=5001

# Database configuration - PostgreSQL for testing
DB_DIALECT=postgres
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=tax_filing_test
TEST_DB_USER=postgres
TEST_DB_PASSWORD=password
DB_LOGGING=false

# JWT configuration
JWT_SECRET=test_jwt_secret_key
JWT_EXPIRES_IN=1d

# CORS configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
