import dotenv from 'dotenv';
import { Sequelize } from 'sequelize-typescript';
import models from '../models';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Define global types
declare global {
  var testDb: Sequelize;
}

// Global test setup
beforeAll(async () => {
  console.log('Setting up test environment...');

  try {
    // Create a test database connection using SQLite in-memory for testing
    // Note: In production, PostgreSQL should be used as specified in requirements
    const sequelize = new Sequelize({
      dialect: 'sqlite',
      storage: ':memory:',
      logging: false,
      models: models,
      // Enable foreign key constraints for SQLite
      dialectOptions: {
        // Enable foreign key constraints
        options: {
          enableForeignKeyConstraints: true
        }
      }
    });

    // Enable foreign key constraints for SQLite
    await sequelize.query('PRAGMA foreign_keys = ON;');

    // Sync models with database (force: true will drop tables and recreate them)
    await sequelize.sync({ force: true });
    console.log('Test database initialized with SQLite in-memory database (with foreign key constraints)');

    // Store the database connection globally
    global.testDb = sequelize;
  } catch (error) {
    console.error('Failed to initialize test database:', error);
    throw error;
  }
});

// Global test teardown
afterAll(async () => {
  console.log('Cleaning up test environment...');

  // Close the test database connection
  if (global.testDb) {
    await global.testDb.close();
  }
});

// Reset database between tests with proper foreign key handling
afterEach(async () => {
  console.log('Test completed, resetting state...');

  try {
    // For SQLite, we need to handle foreign key constraints properly
    if (global.testDb) {
      // Disable foreign key checks temporarily for cleanup
      await global.testDb.query('PRAGMA foreign_keys = OFF;');

      // Delete records in correct order to respect foreign key constraints
      // Delete child records first, then parent records
      const models = global.testDb.models;

      // Order matters: delete child tables first
      const deletionOrder = [
        'TaxCalculation',
        'EstimatedTaxPayment',
        'EarnedIncomeTaxCredit',
        'EducationCredit',
        'ChildDependentCareCredit',
        'ChildTaxCredit',
        'Dependent',
        'ScheduleA',
        'Adjustments',
        'ScheduleSE',
        'ScheduleC',
        'Form1099DIV',
        'Form1099INT',
        'W2StateInfo',
        'W2',
        'UploadedDocument',
        'Taxpayer', // Delete taxpayers before users
        'User'      // Delete users last
      ];

      // Delete records in the specified order
      for (const modelName of deletionOrder) {
        if (models[modelName]) {
          await models[modelName].destroy({
            where: {},
            truncate: true,
            cascade: true,
            force: true
          });
        }
      }

      // Re-enable foreign key checks
      await global.testDb.query('PRAGMA foreign_keys = ON;');

      console.log('Database state reset');
    }
  } catch (error) {
    console.error('Error resetting test database:', error);
    // Re-enable foreign key checks even if cleanup failed
    try {
      if (global.testDb) {
        await global.testDb.query('PRAGMA foreign_keys = ON;');
      }
    } catch (fkError) {
      console.error('Error re-enabling foreign key checks:', fkError);
    }
  }

  // Clear all mocks
  jest.clearAllMocks();
});
